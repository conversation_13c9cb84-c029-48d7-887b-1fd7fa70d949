{"httpMethod": "GET", "path": "/api/v1", "pathParameters": null, "queryStringParameters": null, "headers": {"Content-Type": "application/json", "Host": "7ry7plkxg8.execute-api.us-east-2.amazonaws.com"}, "multiValueHeaders": {}, "body": null, "isBase64Encoded": false, "requestContext": {"accountId": "************", "apiId": "7ry7plkxg8", "httpMethod": "GET", "path": "/prod/api/v1", "stage": "prod", "requestId": "test-request-id", "resourceId": "test-resource-id", "resourcePath": "/{proxy+}", "identity": {"sourceIp": "127.0.0.1"}}, "resource": "/{proxy+}"}