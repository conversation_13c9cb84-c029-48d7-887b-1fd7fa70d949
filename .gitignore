# Compiled output
/dist
/node_modules
*/node_modules
*.tsbuildinfo

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Environment variables
.env
.env.local
.env.development
.env.production

# CDK
infrastructure/cdk.out/
infrastructure/node_modules/
infrastructure/.env
# CDK compiled output
infrastructure/bin/*.js
infrastructure/bin/*.d.ts
infrastructure/lib/*.js
infrastructure/lib/*.d.ts

# AWS
.aws/

# Lambda build artifacts
lambda-package/
lambda-temp-build/

# Temporary files
*.tmp
*.temp
